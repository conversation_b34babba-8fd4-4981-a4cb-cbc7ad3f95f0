management:
  endpoint:
    health:
      showDetails: always
      probes:
        enabled: true

spring:
  jpa:
    properties:
      hibernate:
        id:
          # TODO: Temporary fix issue of sequence "REVISION_INFO_SEQ" not found when upgrading to Hibernate 6
          db_structure_naming_strategy: 'legacy'
  datasource:
    dataSourceClassName: com.prospection.jdbc.PGIamAuthTokenDataSource
    dataSourceProperties:
      serverName: ${SPRING_DATA_SOURCE_SERVER_NAME}
      portNumber: 5432
      user: ${SPRING_DATA_SOURCE_USER}
      databaseName: ${SPRING_DATA_SOURCE_DATABASE_NAME}

tracing:
  # TODO: PF-126 we are going to remove xray completely when upgrade pd-starter-* to the latest version
  enabled: false

application:
  name: ${APPLICATION_NAME}
  security:
    secret: ${JWT_SECRET}
  amazon:
    s3Bucket: ${S3_BUCKET}
    etlQueueUrl: ${ETL_QUEUE_URL}
    lambda:
      itemGroupsSpark: ${LAMBDA_ITEM_GROUPS_SPARK}
    sns:
      jobNotificationTopic: ${SNS_TOPIC_ARN}
  integration:
    dashxServiceUrl: ${DASHX_SERVICE_URL}
    customerServiceUrl: ${CUSTOMER_SERVICE_URL}
    featureToggleService:
      enabled: ${FEATURE_TOGGLE_SERVICE_AVAILABLE}
      url: ${FEATURE_TOGGLE_SERVICE_URL}
      apiToken: ${FEATURE_TOGGLE_SERVICE_API_TOKEN:#{'unused'}}
  job:
    archiveUnusedData:
      cron: ${ARCHIVE_UNUSED_DATA_CRON:#{'-'}}
      timezone: Australia/Sydney
      cutOffMonths: 6
      chunkSize: 20

s3-items-for-external-tool:
  countryPaths:
    au: s3a://pd-au-${ENV_NAME}-common/external-reference-data
    jp: s3a://pd-jp-${ENV_NAME}-common/external-reference-data
    us: s3a://pd-us-${ENV_NAME}-common/external-reference-data


server:
  compression:
    enabled: true
  error:
    include-message: always
  max-http-request-header-size: '20KB'

cloud:
  aws:
    rds:
      enabled: false
    elasticache:
      enabled: false
    stack:
      enabled: false
    sns:
      enabled: false
    sqs:
      endpoint: ${ETL_QUEUE_URL}
    region:
      static: ${AWS_REGION}

