# Spark 4.0 ArtifactManager Fix

## Problem Description

Apache Spark 4.0 introduced a bug in the `ArtifactManager` class (SPARK-52396) where it attempts to create temporary directories in the current working directory instead of the system temp directory. This causes permission issues in Docker environments where the working directory is read-only.

### Error Symptoms
```
java.io.IOException: Failed to create a temp directory (under artifacts) after 10 attempts!
	at org.apache.spark.network.util.JavaUtils.createDirectory(JavaUtils.java:411)
	at org.apache.spark.util.SparkFileUtils.createDirectory(SparkFileUtils.scala:95)
	at org.apache.spark.util.SparkFileUtils.createDirectory$(SparkFileUtils.scala:94)
	at org.apache.spark.util.Utils$.createDirectory(Utils.scala:99)
	at org.apache.spark.util.Utils$.createTempDir(Utils.scala:249)
	at org.apache.spark.sql.artifact.ArtifactManager$.artifactRootDirectory$lzycompute(ArtifactManager.scala:468)
```

## Root Cause

The issue is in `ArtifactManager.scala` line 468 in the original Spark 4.0.0 code:

```scala
// Buggy line in Spark 4.0.0:
private[artifact] lazy val artifactRootDirectory = Utils.createTempDir(ARTIFACT_DIRECTORY_PREFIX).toPath
```

**Problem**: The `Utils.createTempDir()` method has two overloads:
- `createTempDir(namePrefix: String)` - creates temp dir in system temp directory ✅
- `createTempDir(namePrefix: String, dir: File)` - creates temp dir in specified directory

When called with just a string parameter, Scala's type inference can sometimes resolve to the wrong overload, causing it to try creating the directory in the current working directory instead of the system temp directory.

**Solution**: Use named parameter to ensure correct method resolution:
```scala
// Fixed line in our override:
private[artifact] lazy val artifactRootDirectory = Utils.createTempDir(namePrefix = ARTIFACT_DIRECTORY_PREFIX).toPath
```

## Official Fix Status

- **Fixed in**: Apache Spark 4.0.1 and 4.1.0
- **GitHub PR**: https://github.com/apache/spark/pull/51083
- **Expected Release**: August/September 2025 for Spark 4.0.1
- **Current Status**: Merged and awaiting release
- **Workaround Required**: Yes, for Spark 4.0.0 users until 4.0.1 is available

## Fix Implementation

We have implemented a **class override approach** that replaces the problematic `ArtifactManager` class with our own fixed version. This approach is elegant, transparent, and requires no changes to existing application code.

### Files Added

1. **kotlin-spark/src/main/scala/org/apache/spark/sql/artifact/ArtifactManager.scala**
   - Complete replacement of the problematic Spark class
   - Contains the critical fix for SPARK-52396
   - Loaded via classpath precedence

2. **lambdas-spark/src/main/scala/org/apache/spark/sql/artifact/ArtifactManager.scala**
   - Copy of the fixed class for Lambda deployments
   - Ensures consistent behavior across all environments

### How the Class Override Works

1. **Classpath Precedence**: When the JVM loads `org.apache.spark.sql.artifact.ArtifactManager`, it finds our version first in the classpath before the one in the Spark JAR

2. **Fixed Implementation**: Our version contains the critical fix on line 475:
   ```scala
   // Original buggy line (Spark 4.0.0):
   Utils.createTempDir(ARTIFACT_DIRECTORY_PREFIX).toPath

   // Fixed line in our version:
   Utils.createTempDir(namePrefix = ARTIFACT_DIRECTORY_PREFIX).toPath
   ```

3. **Complete Override**: The entire class is replaced, ensuring all methods work correctly together

4. **Transparent Operation**: No changes needed to existing application code - the fix is applied automatically

5. **Build Integration**: The Scala source files are automatically compiled and included in the JAR through the Gradle build process

### Technical Implementation Details

- **Source Location**: `kotlin-spark/src/main/scala/org/apache/spark/sql/artifact/ArtifactManager.scala`
- **Package**: `org.apache.spark.sql.artifact` (exact same as original Spark class)
- **Compilation**: Compiled with Scala 2.13 to match Spark 4.0 requirements
- **JAR Inclusion**: Included in the final application JAR with higher classpath priority than Spark JARs

### Advantages of Class Override Approach

- **Direct Fix**: Fixes the exact problem at its source
- **No System Property Manipulation**: Cleaner than JVM property workarounds
- **Transparent**: No changes needed to existing application code
- **Minimal Impact**: Only affects the specific problematic class
- **Future-Proof**: Easy to remove when Spark 4.0.1 is released
- **Zero Runtime Overhead**: No performance impact compared to system property solutions
- **Maintainable**: Clear, documented approach that's easy to understand and modify
- **Environment Agnostic**: Works consistently across Docker, Lambda, and local environments

## Migration Path

### When Spark 4.0.1 is Released

1. Upgrade to Spark 4.0.1 or later
2. Remove our custom ArtifactManager.scala files:
   - `kotlin-spark/src/main/scala/org/apache/spark/sql/artifact/ArtifactManager.scala`
   - `lambdas-spark/src/main/scala/org/apache/spark/sql/artifact/ArtifactManager.scala`
3. Test thoroughly to ensure the official fix works

## References

- **Spark JIRA**: [SPARK-52396](https://issues.apache.org/jira/browse/SPARK-52396)
- **GitHub PR**: https://github.com/apache/spark/pull/51083
- **Spark Documentation**: https://spark.apache.org/docs/latest/
- **Class Loading in JVM**: https://docs.oracle.com/javase/specs/jvms/se17/html/jvms-5.html
