import org.apache.spark.sql.SparkSession

object TestSpark {
    val spark: SparkSession = SparkSession.builder()
        .appName("Test Spark App")
        .master("local[1]")
        .config("spark.sql.shuffle.partitions", 1)
        .config("spark.default.parallelism", 1)
        .config("spark.sql.adaptive.enabled", "false")
        .config("spark.shuffle.compress", "false")
        .config("spark.broadcast.compress", "false")
        .config("spark.ui.enabled", "false")
        .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
        .config("spark.kryo.unsafe", "true")
        .config("spark.kryo.registrationRequired", "false")
        // Java 17 compatibility for Kryo serialization
        .config(
            "spark.driver.extraJavaOptions",
            "--add-opens java.base/java.lang.invoke=ALL-UNNAMED " +
            "--add-opens java.base/sun.nio.ch=ALL-UNNAMED " +
            "--add-opens java.base/java.nio=ALL-UNNAMED " +
            "--add-opens java.base/java.lang=ALL-UNNAMED " +
            "--add-opens java.base/java.util=ALL-UNNAMED " +
            "--add-opens java.base/sun.nio.cs=ALL-UNNAMED"
        )
        .getOrCreate()
        .also { it.sparkContext().setLogLevel("WARN") }
}