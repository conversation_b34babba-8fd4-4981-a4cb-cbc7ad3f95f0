package com.prospection.refdata.config

import com.prospection.refdata.common.SparkConfig
import org.apache.spark.sql.SparkSession

object SparkConfiguration {
    // Do not use by lazy here to utilise AWS Lambda 10s initialisation CPU bust
    val spark: SparkSession = run {
        val numCores = System.getenv("NUM_CORES")?.toInt() ?: 1
        println("Available vCPUs: ${Runtime.getRuntime().availableProcessors()}")
        println("Cores to use: $numCores")
        val sparkConfig = SparkConfig.getCloudSparkConfig()
            .set("spark.master", "local[$numCores]")
            .set("spark.sql.shuffle.partitions", numCores.toString())
            .set("spark.default.parallelism", numCores.toString())
            .set("spark.sql.adaptive.enabled", "false")
            .set("spark.shuffle.compress", "false")
            .set("spark.broadcast.compress", "false")
            .set("spark.ui.enabled", "false")
            // AWS Lambda networking configuration - critical for Lambda environment
            .set("spark.driver.bindAddress", "127.0.0.1")
            .set("spark.driver.host", "127.0.0.1")
            .set("spark.executor.instances", "0") // No separate executors in local mode
            .set("spark.dynamicAllocation.enabled", "false")
            .set("spark.shuffle.service.enabled", "false")
            // Prevent any external network communication attempts
            .set("spark.driver.port", "0") // Let system assign available port
            .set("spark.blockManager.port", "0")
            // Kryo serialization configuration for Java 17 compatibility
            .set("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
//            .set("spark.kryo.registrationRequired", "false") // Allow unregistered classes
            .set("spark.kryoserializer.buffer", "64m") // Smaller buffer for Lambda
            .set("spark.kryoserializer.buffer.max", "256m") // Smaller max buffer for Lambda
            // Disable problematic Kryo optimizations that cause Java 17 module issues
//            .set("spark.kryo.unsafe", "false")
            .set("spark.kryo.referenceTracking", "true")
        val spark = SparkSession.builder()
            .appName("pd-ref-data-service-v2")
            .config(sparkConfig)
            .orCreate
        spark.sparkContext().setLogLevel("WARN")
        spark
    }
}