package com.prospection.refdata.config

import com.esotericsoftware.kryo.Kryo
import com.esotericsoftware.kryo.serializers.JavaSerializer
import org.apache.spark.serializer.KryoRegistrator
import org.slf4j.LoggerFactory

/**
 * Lambda-specific Kryo registrator that handles Java 17 module system compatibility.
 * 
 * This registrator specifically addresses the java.nio.HeapByteBuffer serialization issue
 * that occurs in AWS Lambda environments with Java 17 due to module access restrictions.
 */
class LambdaKryoRegistrator extends KryoRegistrator {
  
  private val logger = LoggerFactory.getLogger(classOf[LambdaKryoRegistrator])
  
  override def registerClasses(kryo: Kryo): Unit = {
    logger.info("Registering Lambda-specific Kryo classes for Java 17 compatibility")
    
    try {
      // Use JavaSerializer for problematic NIO classes to avoid reflection issues
      kryo.register(classOf[java.nio.HeapByteBuffer], new JavaSerializer())
      kryo.register(classOf[java.nio.DirectByteBuffer], new JavaSerializer())
      kryo.register(classOf[java.nio.ByteBuffer], new JavaSerializer())
      
      // Register other common problematic classes
      kryo.register(Class.forName("java.nio.HeapByteBufferR"), new JavaSerializer())
      kryo.register(Class.forName("java.nio.DirectByteBufferR"), new JavaSerializer())
      
      logger.info("Successfully registered Lambda-specific Kryo serializers")
      
    } catch {
      case e: ClassNotFoundException =>
        logger.warn(s"Some NIO classes not found, this is expected in some environments: ${e.getMessage}")
      case e: Exception =>
        logger.error("Error registering Lambda-specific Kryo classes", e)
        throw e
    }
    
    // Set Kryo to be more lenient with unregistered classes
    kryo.setWarnUnregisteredClasses(false)
    kryo.setRegistrationRequired(false)
    
    logger.debug("Finished Lambda Kryo registration")
  }
}
